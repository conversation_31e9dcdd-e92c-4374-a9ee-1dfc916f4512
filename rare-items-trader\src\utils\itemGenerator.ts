import { Item, ItemCategory, ItemCondition } from '../types/game';

const ITEM_NAMES = {
  antique: ['Victorian Clock', 'Ancient Vase', 'Antique Chair', 'Vintage Mirror', 'Classic Typewriter'],
  collectible: ['Rare Coin', 'Comic Book', 'Trading Card', 'Action Figure', 'Stamp Collection'],
  art: ['Oil Painting', 'Sculpture', 'Watercolor', 'Sketch', 'Digital Art'],
  jewelry: ['Diamond Ring', 'Gold Necklace', 'Pearl Earrings', 'Sapphire Bracelet', 'Emerald Pendant'],
};

const CONDITIONS: ItemCondition[] = ['mint', 'good', 'fair', 'poor'];

export function generateRandomItem(): Item {
  const category = Object.keys(ITEM_NAMES)[Math.floor(Math.random() * Object.keys(ITEM_NAMES).length)] as ItemCategory;
  const nameList = ITEM_NAMES[category];
  const name = nameList[Math.floor(Math.random() * nameList.length)];
  const rarity = Math.floor(Math.random() * 10) + 1;
  const condition = CONDITIONS[Math.floor(Math.random() * CONDITIONS.length)];
  const baseValue = Math.floor(Math.random() * 1000) + 100 * rarity;

  return {
    id: Math.random().toString(36).substr(2, 9),
    name,
    category,
    baseValue,
    condition,
    rarity,
    description: `A ${condition} condition ${name.toLowerCase()} of rarity ${rarity}/10.`,
  };
}

export function generateMarketItems(count: number): Item[] {
  return Array.from({ length: count }, () => generateRandomItem());
} 