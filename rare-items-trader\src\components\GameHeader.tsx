import { AppBar, Too<PERSON>bar, Typo<PERSON>, Button } from '@mui/material';
import { useGameStore } from '../store/gameStore';

const GameHeader = () => {
  const { day, advanceDay } = useGameStore();

  return (
    <AppBar position="static" sx={{ mb: 4 }}>
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          Rare Items Trader
        </Typography>
        <Typography variant="body1" sx={{ mr: 2 }}>
          Day: {day}
        </Typography>
        <Button color="inherit" onClick={advanceDay}>
          Next Day
        </Button>
      </Toolbar>
    </AppBar>
  );
};

export default GameHeader; 