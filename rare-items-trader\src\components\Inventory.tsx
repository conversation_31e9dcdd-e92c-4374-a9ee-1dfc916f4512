import { Paper, Typography, Grid, Card, CardContent, CardActions, Button } from '@mui/material';
import { useGameStore } from '../store/gameStore';

const Inventory = () => {
  const { player, sellItem } = useGameStore();

  return (
    <Paper sx={{ p: 2 }}>
      <Typography variant="h5" sx={{ mb: 2 }}>
        Your Inventory
      </Typography>
      <Grid container spacing={2}>
        {player.inventory.map((item) => (
          <Grid item xs={12} sm={6} md={4} key={item.id}>
            <Card>
              <CardContent>
                <Typography variant="h6">{item.name}</Typography>
                <Typography color="textSecondary">Category: {item.category}</Typography>
                <Typography>Condition: {item.condition}</Typography>
                <Typography>Rarity: {item.rarity}/10</Typography>
                <Typography>Purchase Price: ${item.purchasePrice?.toLocaleString()}</Typography>
                <Typography variant="body2">{item.description}</Typography>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  color="secondary"
                  onClick={() => sellItem(item.id)}
                >
                  Sell
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
        {player.inventory.length === 0 && (
          <Grid item xs={12}>
            <Typography color="textSecondary" align="center">
              Your inventory is empty. Visit the market to buy some items!
            </Typography>
          </Grid>
        )}
      </Grid>
    </Paper>
  );
};

export default Inventory; 