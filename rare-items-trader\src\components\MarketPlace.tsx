import { Paper, Typography, Grid, Card, CardContent, CardActions, Button } from '@mui/material';
import { useGameStore } from '../store/gameStore';
import { Item } from '../types/game';

interface MarketPlaceProps {
  items: Item[];
}

const MarketPlace = ({ items }: MarketPlaceProps) => {
  const { buyItem, player } = useGameStore();

  return (
    <Paper sx={{ p: 2, mb: 4 }}>
      <Typography variant="h5" sx={{ mb: 2 }}>
        Market Place
      </Typography>
      <Grid container spacing={2}>
        {items.map((item) => (
          <Grid item xs={12} sm={6} md={4} key={item.id}>
            <Card>
              <CardContent>
                <Typography variant="h6">{item.name}</Typography>
                <Typography color="textSecondary">Category: {item.category}</Typography>
                <Typography>Condition: {item.condition}</Typography>
                <Typography>Rarity: {item.rarity}/10</Typography>
                <Typography>Value: ${item.baseValue.toLocaleString()}</Typography>
                <Typography variant="body2">{item.description}</Typography>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  color="primary"
                  onClick={() => buyItem(item)}
                  disabled={player.money < item.baseValue || player.inventory.length >= player.storageSpace}
                >
                  Buy
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default MarketPlace; 