import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Item, Player, MarketEvent } from '../types/game';

interface GameState {
  player: Player;
  marketItems: Item[];
  activeEvents: MarketEvent[];
  day: number;
  
  // Actions
  initializeGame: () => void;
  buyItem: (item: Item) => void;
  sellItem: (itemId: string) => void;
  advanceDay: () => void;
  addMarketEvent: (event: MarketEvent) => void;
}

const INITIAL_PLAYER: Player = {
  money: 10000,
  inventory: [],
  reputation: 0,
  storageSpace: 10,
  storageCost: 100,
};

export const useGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      player: INITIAL_PLAYER,
      marketItems: [],
      activeEvents: [],
      day: 1,

      initializeGame: () => {
        set({
          player: INITIAL_PLAYER,
          marketItems: [],
          activeEvents: [],
          day: 1,
        });
      },

      buyItem: (item: Item) => {
        const { player, marketItems } = get();
        if (player.money >= item.baseValue && player.inventory.length < player.storageSpace) {
          set({
            player: {
              ...player,
              money: player.money - item.baseValue,
              inventory: [...player.inventory, { ...item, purchaseDate: new Date(), purchasePrice: item.baseValue }],
            },
            marketItems: marketItems.filter(i => i.id !== item.id),
          });
        }
      },

      sellItem: (itemId: string) => {
        const { player } = get();
        const item = player.inventory.find(i => i.id === itemId);
        if (item) {
          const sellPrice = calculateSellPrice(item);
          set({
            player: {
              ...player,
              money: player.money + sellPrice,
              inventory: player.inventory.filter(i => i.id !== itemId),
            },
          });
        }
      },

      advanceDay: () => {
        const { day, player } = get();
        set({
          day: day + 1,
          player: {
            ...player,
            money: player.money - player.storageCost,
          },
        });
      },

      addMarketEvent: (event: MarketEvent) => {
        set(state => ({
          activeEvents: [...state.activeEvents, event],
        }));
      },
    }),
    {
      name: 'game-storage',
    }
  )
);

function calculateSellPrice(item: Item): number {
  const conditionMultiplier = {
    mint: 1.2,
    good: 1.0,
    fair: 0.8,
    poor: 0.5,
  }[item.condition];

  const rarityMultiplier = 1 + (item.rarity * 0.1);
  
  return Math.floor(item.baseValue * conditionMultiplier * rarityMultiplier);
} 