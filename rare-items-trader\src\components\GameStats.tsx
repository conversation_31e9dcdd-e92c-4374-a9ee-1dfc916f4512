import { Paper, Grid, Typography } from '@mui/material';
import { useGameStore } from '../store/gameStore';

const GameStats = () => {
  const { player } = useGameStore();

  return (
    <Paper sx={{ p: 2, mb: 4 }}>
      <Grid container spacing={2}>
        <Grid item xs={4}>
          <Typography variant="h6">Money</Typography>
          <Typography variant="body1">${player.money.toLocaleString()}</Typography>
        </Grid>
        <Grid item xs={4}>
          <Typography variant="h6">Inventory</Typography>
          <Typography variant="body1">
            {player.inventory.length}/{player.storageSpace} items
          </Typography>
        </Grid>
        <Grid item xs={4}>
          <Typography variant="h6">Storage Cost</Typography>
          <Typography variant="body1">${player.storageCost}/day</Typography>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default GameStats; 