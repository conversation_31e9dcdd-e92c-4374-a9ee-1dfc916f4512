import { useEffect } from 'react';
import { Container, CssBaseline, ThemeProvider, createTheme } from '@mui/material';
import { useGameStore } from './store/gameStore';
import { generateMarketItems } from './utils/itemGenerator';
import GameHeader from './components/GameHeader';
import MarketPlace from './components/MarketPlace';
import Inventory from './components/Inventory';
import GameStats from './components/GameStats';

const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#90caf9',
    },
    secondary: {
      main: '#f48fb1',
    },
  },
});

function App() {
  const { initializeGame, marketItems, day } = useGameStore();

  useEffect(() => {
    initializeGame();
  }, []);

  useEffect(() => {
    // Generate new market items every 3 days
    if (day % 3 === 0) {
      useGameStore.setState({ marketItems: generateMarketItems(5) });
    }
  }, [day]);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <GameHeader />
        <GameStats />
        <MarketPlace items={marketItems} />
        <Inventory />
      </Container>
    </ThemeProvider>
  );
}

export default App; 